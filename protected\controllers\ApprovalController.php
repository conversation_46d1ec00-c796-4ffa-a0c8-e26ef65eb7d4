<?php

class ApprovalController extends Controller
{
    /**
     * Access control for approval page - GM and CM roles only via magic login
     */
    public function accessRules() {
        return array(
            array('allow',
                'actions' => array('index', 'approve', 'getRobustnessDetails', 'getFinePenaltyDetails'),
                'users' => array('*'), // Allow all users initially, authentication handled in action
            ),
            array('deny', 'users' => array('*')),
        );
    }

    /**
     * Approval page accessible via magic login links
     */
    public function actionIndex()
    {
        try {
            // Extract URL parameters
            $craRequestId = Yii::app()->request->getParam('craRequestId');
            $loginHash = Yii::app()->request->getParam('loginHash');
            $email = Yii::app()->request->getParam('email');

            // Validate required parameters
            if (empty($craRequestId) || empty($loginHash) || empty($email)) {
                throw new CHttpException(400, 'Missing required parameters: craRequestId, loginHash, email');
            }

            // Authenticate via magic login
            $authResult = ApprovalService::authenticateQuickLogin($craRequestId, $loginHash, $email);

            if (!$authResult || !isset($authResult['success']) || !$authResult['success']) {
                throw new CHttpException(401, 'Authentication failed: ' . (isset($authResult['error']) ? $authResult['error'] : 'Invalid credentials'));
            }

            // Verify user has GM or CM role
            $userInfo = $authResult['data'];
            if (!in_array($userInfo['groupName'], array('GM-CPD', 'CM-CPD', 'GM', 'CM'))) {
                throw new CHttpException(403, 'Access denied. GM and CM roles only.');
            }

            // Store authentication data in session for subsequent API calls
            $session = Yii::app()->session;
            $session->open(); // Ensure session is open
            $session['quickLoginToken'] = $userInfo['id'];
            $session['quickLoginUser'] = $userInfo;
            $session['quickLoginCraId'] = $craRequestId;

            Yii::log("Stored in session - Token: {$userInfo['id']}, User: {$userInfo['fullName']}", CLogger::LEVEL_INFO, 'approval');

            // Get CRA claims data for the table
            Yii::log("Attempting to get claims data for CRA ID: {$craRequestId} with token: {$userInfo['id']}", CLogger::LEVEL_INFO, 'approval');
            $claimsData = ApprovalService::getCraClaimsData($craRequestId, $userInfo['id']);
            Yii::log("Claims data retrieved: " . count($claimsData) . " claims found", CLogger::LEVEL_INFO, 'approval');

            // If no claims data is available, this could indicate an API issue or no claims exist
            // The view will handle displaying appropriate messages for empty data

            // Render the approval page with headerless layout
            $this->layout = 'approval'; // Custom layout without header
            $this->render('index', array(
                'craRequestId' => $craRequestId,
                'userInfo' => $userInfo,
                'claimsData' => $claimsData
            ));

        } catch (Exception $e) {
            // Log the error
            Yii::log('Approval page error: ' . $e->getMessage(), CLogger::LEVEL_ERROR, 'approval');
            
            // Show error page
            $this->layout = 'approval';
            $this->render('error', array(
                'error' => $e->getMessage(),
                'code' => $e instanceof CHttpException ? $e->statusCode : 500
            ));
        }
    }

    /**
     * Handle approval actions (approve/not approve)
     * Called via AJAX from the approval page
     */
    public function actionApprove()
    {
        try {
            Yii::log("Approval action called", CLogger::LEVEL_INFO, 'approval');

            // Verify this is an AJAX request
            if (!Yii::app()->request->isAjaxRequest) {
                Yii::log("Not an AJAX request", CLogger::LEVEL_ERROR, 'approval');
                throw new CHttpException(400, 'Invalid request method');
            }

            // Check authentication
            $session = Yii::app()->session;
            $session->open(); // Ensure session is open
            $token = $session['quickLoginToken'];
            $userInfo = $session['quickLoginUser'];

            Yii::log("Session token: " . ($token ? 'exists' : 'missing'), CLogger::LEVEL_INFO, 'approval');
            Yii::log("Session userInfo: " . ($userInfo ? 'exists' : 'missing'), CLogger::LEVEL_INFO, 'approval');

            if ($userInfo) {
                Yii::log("Session userInfo details: " . print_r($userInfo, true), CLogger::LEVEL_INFO, 'approval');
            }

            if (empty($token) || empty($userInfo)) {
                Yii::log("Authentication failed - missing token or userInfo", CLogger::LEVEL_ERROR, 'approval');
                throw new CHttpException(401, 'Authentication required');
            }

            // Get request parameters
            $claimId = Yii::app()->request->getPost('claimId');
            $action = Yii::app()->request->getPost('action'); // 'APPROVED' or 'NOT_APPROVED'

            Yii::log("Request parameters - claimId: {$claimId}, action: {$action}", CLogger::LEVEL_INFO, 'approval');

            if (empty($claimId) || empty($action)) {
                Yii::log("Missing parameters - claimId: " . ($claimId ?: 'empty') . ", action: " . ($action ?: 'empty'), CLogger::LEVEL_ERROR, 'approval');
                throw new CHttpException(400, 'Missing required parameters: claimId, action');
            }

            // Validate action
            if (!in_array($action, array('APPROVED', 'NOT_APPROVED'))) {
                Yii::log("Invalid action: {$action}", CLogger::LEVEL_ERROR, 'approval');
                throw new CHttpException(400, 'Invalid action. Must be APPROVED or NOT_APPROVED');
            }

            // Call approval service
            $result = ApprovalService::approveClaim($claimId, $action, $userInfo['groupName'], $token);

            if ($result && isset($result['success']) && $result['success']) {
                Yii::log("Approval successful, sending JSON response", CLogger::LEVEL_INFO, 'approval');

                // Set proper headers for JSON response
                header('Content-Type: application/json');
                echo json_encode(array('success' => true, 'message' => 'Claim ' . strtolower($action) . ' successfully'));
            } else {
                throw new Exception('Failed to update claim status: ' . (isset($result['error']) ? $result['error'] : 'Unknown error'));
            }

        } catch (Exception $e) {
            Yii::log('Approval action error: ' . $e->getMessage(), CLogger::LEVEL_ERROR, 'approval');

            // Set proper headers for JSON response
            header('Content-Type: application/json');
            $statusCode = $e instanceof CHttpException ? $e->statusCode : 500;
            http_response_code($statusCode);
            echo json_encode(array('success' => false, 'error' => $e->getMessage()));
        }

        Yii::app()->end();
    }

    /**
     * Proxy endpoint for robustness details
     */
    public function actionGetRobustnessDetails($id)
    {
        try {
            // Check session authentication
            $session = Yii::app()->session;
            if (!isset($session['quickLoginToken']) || !isset($session['quickLoginUser'])) {
                throw new CHttpException(401, 'Session expired. Please refresh the page.');
            }

            $token = $session['quickLoginToken'];
            $url = "http://localhost:3005/api/robustness/" . $id;

            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_TIMEOUT, 10);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Authorization: Bearer " . $token,
                "Content-Type: application/json"
            ));

            $response = curl_exec($curl);
            $httpStatus = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);

            if ($curlError) {
                throw new Exception("CURL error: " . $curlError);
            }

            if ($httpStatus == 200) {
                header('Content-Type: application/json');
                echo $response;
            } else {
                throw new Exception("API error: HTTP " . $httpStatus);
            }

        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(array(
                'success' => false,
                'error' => $e->getMessage()
            ));
        }
    }

    /**
     * Proxy endpoint for fine and penalty details
     */
    public function actionGetFinePenaltyDetails($id)
    {
        try {
            // Check session authentication
            $session = Yii::app()->session;
            if (!isset($session['quickLoginToken']) || !isset($session['quickLoginUser'])) {
                throw new CHttpException(401, 'Session expired. Please refresh the page.');
            }

            $token = $session['quickLoginToken'];
            $url = "http://localhost:3005/api/fine-and-penalties/" . $id;

            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_TIMEOUT, 10);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Authorization: Bearer " . $token,
                "Content-Type: application/json"
            ));

            $response = curl_exec($curl);
            $httpStatus = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);

            if ($curlError) {
                throw new Exception("CURL error: " . $curlError);
            }

            if ($httpStatus == 200) {
                header('Content-Type: application/json');
                echo $response;
            } else {
                throw new Exception("API error: HTTP " . $httpStatus);
            }

        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(array(
                'success' => false,
                'error' => $e->getMessage()
            ));
        }
    }
}
