<!-- CRA Approval Page -->
<!-- Full-width header outside container to eliminate side padding -->
<div class="approval-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-6 col-sm-6">
                <h2 class="approval-title">CLAIM SYNTHESIS</h2>
            </div>
            <div class="col-md-6 col-sm-6 text-right">
                <p class="approval-user-info">Logged in as: <?php echo $userInfo['fullName']; ?> (<?php echo $userInfo['groupName']; ?>)</p>
            </div>
        </div>
    </div>
</div>

<!-- Main content container -->
<div class="container-fluid approval-container">

    <!-- Request Information Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4>Request Information</h4>
                </div>
                <div class="panel-body">
                    <div class="table-responsive">
                        <table class="table table-bordered approval-info-table">
                            <tbody>
                                <tr>
                                    <td class="info-label"><strong>BRAND</strong></td>
                                    <td class="info-value">
                                        <?php
                                        // Collect all unique brand names from claims
                                        $brandNames = array();
                                        if (!empty($claimsData)) {
                                            foreach ($claimsData as $claim) {
                                                if (isset($claim['brandName']) && !empty($claim['brandName']) && !in_array($claim['brandName'], $brandNames)) {
                                                    $brandNames[] = $claim['brandName'];
                                                }
                                            }
                                        }
                                        echo !empty($brandNames) ? implode(', ', $brandNames) : 'N/A';
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="info-label"><strong>Product Name</strong></td>
                                    <td class="info-value">
                                        <?php
                                        // Collect all unique product names from claims
                                        $productNames = array();
                                        if (!empty($claimsData)) {
                                            foreach ($claimsData as $claim) {
                                                if (isset($claim['productName']) && !empty($claim['productName']) && !in_array($claim['productName'], $productNames)) {
                                                    $productNames[] = $claim['productName'];
                                                }
                                            }
                                        }
                                        echo !empty($productNames) ? implode(', ', $productNames) : 'N/A';
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="info-label"><strong>Claim Assessor</strong></td>
                                    <td class="info-value">
                                        <?php echo isset($claim['claimAssessor']) ? $claim['claimAssessor'] : ''; ?>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Approve Claims Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4>Approve Claims</h4>
                </div>
                <div class="panel-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped approval-table" id="claims-approval-table">
                            <thead>
                                <tr>
                                    <th class="approval-header-cell">Last Update</th>
                                    <th class="approval-header-cell">Exposition</th>
                                    <th class="approval-header-cell">Claims</th>
                                    <th class="approval-header-cell">Robustness</th>
                                    <th class="approval-header-cell">Detail</th>
                                    <th class="approval-header-cell critical-risk-header">Critical Risk</th>
                                    <th class="approval-header-cell">Fine and Penalty</th>
                                    <th class="approval-header-cell">Approval</th>
                                    <th class="approval-header-cell">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="claims-approval-tbody">
                                <?php if (!empty($claimsData)): ?>
                                    <?php foreach ($claimsData as $claim): ?>
                                    <tr data-claim-id="<?php echo isset($claim['id']) ? $claim['id'] : ''; ?>">
                                        <!-- Last Update -->
                                        <td class="approval-cell">
                                            <?php
                                            if (isset($claim['updated']) && !empty($claim['updated'])) {
                                                echo date('d/m/Y H:i', $claim['updated'] / 1000);
                                            }
                                            ?>
                                        </td>

                                        <!-- Exposition -->
                                        <td class="approval-cell">
                                            <?php echo isset($claim['exposition']) ? $claim['exposition'] : ''; ?>
                                        </td>

                                        <!-- Claims -->
                                        <td class="approval-cell">
                                            <?php echo isset($claim['claims']) ? $claim['claims'] : ''; ?>
                                        </td>

                                        <!-- Robustness (Clickable link) -->
                                        <td class="approval-cell">
                                            <?php if (isset($claim['robustnessId']) && !empty($claim['robustnessId'])): ?>
                                                <a href="#" class="robustness-link" data-robustness-id="<?php echo $claim['robustnessId']; ?>">
                                                    <?php echo isset($claim['robustnessName']) ? $claim['robustnessName'] : 'Robustness (ID: ' . $claim['robustnessId'] . ')'; ?>
                                                </a>
                                            <?php endif; ?>
                                        </td>

                                        <!-- Detail -->
                                        <td class="approval-cell">
                                            <?php echo isset($claim['detail']) ? $claim['detail'] : ''; ?>
                                        </td>

                                        <!-- Critical Risk (Purple background) -->
                                        <td class="approval-cell critical-risk-cell">
                                            <?php if (isset($claim['criticalRisk']) && !empty($claim['criticalRisk'])): ?>
                                                <span class="badge critical-risk-badge">
                                                    <?php echo $claim['criticalRisk']; ?>
                                                </span>
                                            <?php endif; ?>
                                        </td>

                                        <!-- Fine and Penalty (Clickable link) -->
                                        <td class="approval-cell">
                                            <?php if (isset($claim['fineAndPenaltyId']) && !empty($claim['fineAndPenaltyId'])): ?>
                                                <a href="#" class="fine-penalty-link" data-fine-penalty-id="<?php echo $claim['fineAndPenaltyId']; ?>">
                                                    <?php echo isset($claim['fineAndPenaltyName']) ? $claim['fineAndPenaltyName'] : 'Fine & Penalty (ID: ' . $claim['fineAndPenaltyId'] . ')'; ?>
                                                </a>
                                            <?php endif; ?>
                                        </td>

                                        <!-- Approval Status -->
                                        <td class="approval-cell approval-status-cell">
                                            <?php if (isset($claim['approvalStatus']) && !empty($claim['approvalStatus'])): ?>
                                                <span class="approval-status <?php echo strtolower($claim['approvalStatus']); ?>">
                                                    <?php echo $claim['approvalStatus']; ?>
                                                    <?php if (isset($claim['approvalDate'])): ?>
                                                        <br><small><?php echo date('d/m/Y', strtotime($claim['approvalDate'])); ?></small>
                                                    <?php endif; ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="approval-status pending">Pending</span>
                                            <?php endif; ?>
                                        </td>

                                        <!-- Actions -->
                                        <td class="approval-cell actions-cell">
                                            <?php if (!isset($claim['approvalStatus']) || empty($claim['approvalStatus'])): ?>
                                                <button class="btn btn-success btn-sm approve-btn"
                                                        data-claim-id="<?php echo $claim['id']; ?>"
                                                        data-action="APPROVED">
                                                    <i class="fa fa-check"></i> Approve
                                                </button>
                                                <button class="btn btn-danger btn-sm not-approve-btn"
                                                        data-claim-id="<?php echo $claim['id']; ?>"
                                                        data-action="NOT_APPROVED">
                                                    <i class="fa fa-times"></i> Not Approve
                                                </button>
                                            <?php else: ?>
                                                <span class="text-muted">Action completed</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="12" class="text-center approval-cell">
                                            <div class="no-claims-message">
                                                <i class="fa fa-exclamation-triangle"></i>
                                                <strong>No Claims Data Available</strong><br>
                                                <small class="text-muted">
                                                    Unable to retrieve claims for CRA Request #<?php echo $craRequestId; ?>
                                                </small>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Messages - Moved to top-right corner via CSS -->
</div>
<!-- / container-fluid -->

<!-- Robustness Modal -->
<div class="modal fade" id="robustness-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Robustness Details</h4>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Item Name</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody id="robustness-details">
                            <!-- Populated via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Fine and Penalty Modal -->
<div class="modal fade" id="fine-penalty-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Fine and Penalty Details</h4>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Item Name</th>
                                <th>Description of Acts</th>
                                <th>Penalties/Sanctions Applied</th>
                                <th>Other Remedies</th>
                                <th>Legal Background</th>
                            </tr>
                        </thead>
                        <tbody id="fine-penalty-details">
                            <!-- Populated via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden data for JavaScript -->
<script>
    // Pass data to JavaScript
    window.approvalData = {
        craRequestId: <?php echo json_encode($craRequestId); ?>,
        userInfo: <?php echo json_encode($userInfo); ?>,
        claimsData: <?php echo json_encode($claimsData); ?>,
        baseUrl: '<?php echo Yii::app()->baseUrl; ?>',
        apiBaseUrl: 'http://localhost:3005'
    };
</script>
