<!-- Claims List Table (Top Section) -->
<div class="row">
	<div class="col-md-12">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4>Claims List</h4>
			</div>
			<div class="panel-body">
				<div class="table-responsive">
					<table class="table table-bordered table-striped table-hover">
						<thead>
							<tr>
								<th>Claims</th>
								<th>Robustness</th>
								<th>Detail</th>
								<th style="background-color: #6c757d; color: white;">Framed Risk</th>
								<th style="background-color: #6f42c1; color: white;">Critical Risk</th>
								<th>Fine and Penalty</th>
								<th>MKT Accepted</th>
								<th>Approval</th>
								<th>Actions</th>
							</tr>
						</thead>
						<tbody id="claims-list">
							<?php if(isset($claims) && !empty($claims)): ?>
								<?php foreach($claims as $claim): ?>
								<tr data-claim-id="<?php echo $claim->id; ?>">
									<!-- Claims -->
									<td class="claim-text"><?php echo isset($claim->claims) ? $claim->claims : ''; ?></td>

									<!-- Robustness (Clickable link) -->
									<td>
										<?php if(isset($claim->robustnessId)): ?>
											<a href="#" class="robustness-link" data-robustness-id="<?php echo $claim->robustnessId; ?>">
												<?php echo isset($claim->robustnessLevel) ? $claim->robustnessLevel : 'A'; ?>
											</a>
										<?php endif; ?>
									</td>

									<!-- Detail -->
									<td class="claim-detail"><?php echo isset($claim->detail) ? $claim->detail : ''; ?></td>
									
									<!-- Framed Risk (Gray background) -->
									<td>
										<?php if(isset($claim->framedRisk)): ?>
											<span class="badge" style="background-color: #6c757d;">
												<?php echo $claim->framedRisk; ?>
											</span>
										<?php endif; ?>
									</td>
									
									<!-- Critical Risk (Purple background) -->
									<td>
										<?php if(isset($claim->criticalRisk)): ?>
											<span class="badge" style="background-color: #6f42c1;">
												<?php echo $claim->criticalRisk; ?>
											</span>
										<?php endif; ?>
									</td>
									
									<!-- Fine and Penalty (Clickable link) -->
									<td>
										<?php if(isset($claim->fineAndPenaltyId)): ?>
											<a href="#" class="fine-penalty-link" data-fine-penalty-id="<?php echo $claim->fineAndPenaltyId; ?>">
												<?php echo isset($claim->fineAndPenaltyDescription) ? $claim->fineAndPenaltyDescription : 'Fine/Penalty'; ?>
											</a>
										<?php endif; ?>
									</td>
									
									<!-- MKT Accepted -->
									<td class="mkt-accepted">
										<?php if(isset($claim->mktAcceptedStatus) && !empty($claim->mktAcceptedStatus)): ?>
											<?php echo $claim->mktAcceptedStatus . ' (' . date('d/m/Y', strtotime($claim->mktAcceptedDate)) . ')'; ?>
										<?php endif; ?>
									</td>
									
									<!-- Approval -->
									<td class="approval-status">
										<?php if(isset($claim->approvalStatus) && !empty($claim->approvalStatus)): ?>
											<?php echo $claim->approvalStatus . ' (' . date('d/m/Y', strtotime($claim->approvalDate)) . ')'; ?>
										<?php endif; ?>
									</td>
									
									<!-- Actions -->
									<td class="text-center">
										<?php if(in_array(Yii::app()->user->groupName, array("SCI Manager", "SCI Staff"))): ?>
											<button class="btn btn-sm btn-primary edit-claim" data-claim-id="<?php echo $claim->id; ?>">
												<i class="fa fa-edit"></i> Edit
											</button>
											<button class="btn btn-sm btn-danger delete-claim" data-claim-id="<?php echo $claim->id; ?>">
												<i class="fa fa-trash"></i> Delete
											</button>
										<?php elseif(in_array(Yii::app()->user->groupName, array("Marketing", "CPD", "ACD", "PPD", "LUXE"))): ?>
											<?php if(!isset($claim->mktAcceptedStatus) || empty($claim->mktAcceptedStatus)): ?>
												<button class="btn btn-sm btn-success accept-claim" data-claim-id="<?php echo $claim->id; ?>">
													<i class="fa fa-check"></i> Accept
												</button>
												<button class="btn btn-sm btn-warning reject-claim" data-claim-id="<?php echo $claim->id; ?>">
													<i class="fa fa-times"></i> Not Accept
												</button>
											<?php endif; ?>
										<?php endif; ?>
									</td>
								</tr>
								<?php endforeach; ?>
							<?php else: ?>
								<tr>
									<td colspan="9" class="text-center">No claims found</td>
								</tr>
							<?php endif; ?>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Claim Form (Bottom Section - SCI Only) -->
<?php if(in_array(Yii::app()->user->groupName, array("SCI Manager", "SCI Staff")) && isset($showClaimForm) && $showClaimForm): ?>
<div class="row">
	<div class="col-md-12">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4>Add/Edit Claim</h4>
			</div>
			<div class="panel-body">
				<form id="claim-form" class="form-horizontal">
					<input type="hidden" id="claim-id" name="claimId">
					<input type="hidden" name="craId" value="<?php echo isset($item->id) ? $item->id : 'NOT_SET'; ?>">


					<!-- Claim Type -->
					<div class="form-group">
						<label class="col-sm-2 control-label">Claim Type</label>
						<div class="col-sm-10">
							<div class="radio-inline">
								<label>
									<input type="radio" name="claimType" value="text" checked> Text
								</label>
							</div>
							<div class="radio-inline">
								<label>
									<input type="radio" name="claimType" value="image"> Image
								</label>
							</div>
						</div>
					</div>

					<!-- Claims -->
					<div class="form-group" id="claims-text-group">
						<label class="col-sm-2 control-label">Claims</label>
						<div class="col-sm-10">
							<input type="text" class="form-control" id="claim-text" name="claims" required>
						</div>
					</div>

					<!-- Claims Image Upload -->
					<div class="form-group" id="claims-image-group" style="display: none;">
						<label class="col-sm-2 control-label">Claims Image</label>
						<div class="col-sm-10">
							<input type="file" class="form-control" id="claim-image" name="claimImage" accept="image/*">
							<div id="claim-image-preview" style="margin-top: 10px;"></div>
						</div>
					</div>
					
					<!-- Robustness -->
					<div class="form-group">
						<label class="col-sm-2 control-label">Robustness</label>
						<div class="col-sm-10">
							<select class="form-control" id="robustness" name="robustnessId" required>
								<option value="">Select Robustness</option>
								<?php if(isset($robustnessOptions) && !empty($robustnessOptions)): ?>
									<?php foreach($robustnessOptions as $option): ?>
										<option value="<?php echo $option->id; ?>"><?php echo $option->itemName; ?></option>
									<?php endforeach; ?>
								<?php endif; ?>
							</select>
						</div>
					</div>
					
					<!-- Detail -->
					<div class="form-group">
						<label class="col-sm-2 control-label">Detail</label>
						<div class="col-sm-10">
							<input type="text" class="form-control" id="claim-detail" name="claimDetail" required>
						</div>
					</div>
					
					<!-- Framed Risk -->
					<div class="form-group">
						<label class="col-sm-2 control-label">Framed Risk</label>
						<div class="col-sm-10">
							<select class="form-control framed-risk" id="framed-risk" name="framedRisk" style="background-color: #6c757d; color: white;">
								<option value="">Select Framed Risk</option>
								<option value="IIB">IIB</option>
								<option value="IIIB">IIIB</option>
								<option value="IC">IC</option>
								<option value="IIB">IIB</option>
							</select>
						</div>
					</div>
					
					<!-- Critical Risk -->
					<div class="form-group">
						<label class="col-sm-2 control-label">Critical Risk</label>
						<div class="col-sm-10">
							<select class="form-control critical-risk" id="critical-risk" name="criticalRisk" style="background-color: #6f42c1; color: white;">
								<option value="">Select Critical Risk</option>
								<option value="IIIC">IIIC</option>
								<option value="ID">ID</option>
								<option value="IID">IID</option>
								<option value="IIID">IIID</option>
							</select>
						</div>
					</div>
					
					<!-- Fine and Penalty -->
					<div class="form-group">
						<label class="col-sm-2 control-label">Fine and Penalty</label>
						<div class="col-sm-10">
							<select class="form-control" id="fine-penalty" name="fineAndPenaltyId" required>
								<option value="">Select Fine and Penalty</option>
								<?php if(isset($finePenaltyOptions) && !empty($finePenaltyOptions)): ?>
									<?php foreach($finePenaltyOptions as $option): ?>
										<option value="<?php echo $option->id; ?>"><?php echo $option->itemName; ?></option>
									<?php endforeach; ?>
								<?php endif; ?>
							</select>
						</div>
					</div>
					
					<!-- Form Actions -->
					<div class="form-group">
						<div class="col-sm-offset-2 col-sm-10">
							<button type="button" class="btn btn-primary" id="add-claim">Add</button>
							<button type="button" class="btn btn-default" id="cancel-claim">Cancel</button>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
<?php endif; ?>

<style>
/* Remove border from Claims Image file input field */
#claim-image {
	border: none !important;
	box-shadow: none !important;
	padding: 6px 0 !important;
}

#claim-image:focus {
	border: none !important;
	box-shadow: none !important;
	outline: none !important;
}
</style>

<script>
// Ensure jQuery is available before proceeding
function waitForJQuery(callback) {
    if (typeof $ !== 'undefined' && typeof $.fn !== 'undefined') {
        callback();
    } else {

        setTimeout(function() {
            waitForJQuery(callback);
        }, 100);
    }
}

// Main initialization function
function initializeClaimsSection() {
    // Initialize claims data from server

// Set user role and page context for role-based functionality
window.userGroupName = '<?php echo Yii::app()->user->groupName; ?>';
window.currentPage = '<?php echo Yii::app()->controller->action->id; ?>'; // 'update' or 'process'
window.apiToken = '<?php echo Yii::app()->session["token"]; ?>';
window.userId = <?php echo Yii::app()->user->id; ?>;

// Pass robustness and fine/penalty options to JavaScript
window.robustnessOptions = <?php echo json_encode(isset($robustnessOptions) ? $robustnessOptions : array()); ?>;
window.finePenaltyOptions = <?php echo json_encode(isset($finePenaltyOptions) ? $finePenaltyOptions : array()); ?>;


<?php if(isset($claims) && !empty($claims)): ?>

function initializeClaimsFromServer() {
	// Check if jQuery is available
	if (typeof $ === 'undefined') {
		// jQuery not loaded yet, wait and try again
		setTimeout(initializeClaimsFromServer, 100);
		return;
	}

	$(document).ready(function() {
		// Initialize claims data after the external script is loaded
		if (typeof window.initializeClaimsData === 'function') {
			window.initializeClaimsData(<?php echo json_encode($claims); ?>);
		} else {
			// Script not loaded yet, wait and try again
			setTimeout(initializeClaimsFromServer, 100);
		}
	});
}

// Start the initialization process
initializeClaimsFromServer();


<?php else: ?>

<?php endif; ?>
}

// Start the initialization with jQuery check
waitForJQuery(initializeClaimsSection);
</script>
