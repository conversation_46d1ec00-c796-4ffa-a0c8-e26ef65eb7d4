/**
 * Handles magic login authentication flow, table population, and approval actions
 */

// Namespace for approval page functions to avoid conflicts
var ApprovalPage = {};

$(document).ready(function() {
    // Initialize approval functionality
    initializeApprovalPage();

    // Bind approval action handlers
    bindApprovalActions();

    // Bind modal popup handlers
    bindModalHandlers();

    // Debug functionality available in console
    // Use debugApprovalData() in browser console for troubleshooting
});

/**
 * Initialize the approval page
 */
function initializeApprovalPage() {
    // Check if approval data is available
    if (typeof window.approvalData === 'undefined') {
        console.error('Approval data not available');
        showMessage('Error: Page data not loaded properly', 'error');
        return;
    }

    // Initialize table if needed (data is already rendered server-side)
    initializeTable();
    
    // Show appropriate message based on claims data availability using jubiqAlert
    if (window.approvalData.claimsData && window.approvalData.claimsData.length > 0) {
        jubiqAlert('success', 'Successfully authenticated.');
    } else {
        jubiqAlert('warning', 'Authentication successful, but no claims data found for CRA Request #' + window.approvalData.craRequestId);
    }
}

/**
 * Initialize the claims table
 */
function initializeTable() {
    // Add hover effects to table rows
    $('#claims-approval-table tbody tr').hover(
        function() {
            $(this).addClass('table-row-hover');
        },
        function() {
            $(this).removeClass('table-row-hover');
        }
    );
}

/**
 * Bind modal popup handlers for robustness and fine penalty links
 */
function bindModalHandlers() {
    // Robustness link click handler - Override any existing handlers
    $(document).off('click', '.robustness-link');
    $(document).on('click', '.robustness-link', function(e) {
        e.preventDefault();
        var robustnessId = $(this).data('robustness-id');
        ApprovalPage.showRobustnessDetails(robustnessId);
    });

    // Fine and Penalty link click handler - Override any existing handlers
    $(document).off('click', '.fine-penalty-link');
    $(document).on('click', '.fine-penalty-link', function(e) {
        e.preventDefault();
        var finePenaltyId = $(this).data('fine-penalty-id');
        ApprovalPage.showFinePenaltyDetails(finePenaltyId);
    });
}

/**
 * Bind approval action handlers
 */
function bindApprovalActions() {
    // Approve button handler
    $(document).on('click', '.approve-btn', function(e) {
        e.preventDefault();
        
        const claimId = $(this).data('claim-id');
        const action = $(this).data('action');
        
        if (confirm('Are you sure you want to approve this claim?')) {
            performApprovalAction(claimId, action, $(this));
        }
    });
    
    // Not approve button handler
    $(document).on('click', '.not-approve-btn', function(e) {
        e.preventDefault();
        
        const claimId = $(this).data('claim-id');
        const action = $(this).data('action');
        
        if (confirm('Are you sure you want to reject this claim?')) {
            performApprovalAction(claimId, action, $(this));
        }
    });
}

/**
 * Perform approval action (approve or not approve)
 */
function performApprovalAction(claimId, action, buttonElement) {
    // Disable buttons to prevent double-click
    const row = buttonElement.closest('tr');
    const actionButtons = row.find('.approve-btn, .not-approve-btn');
    actionButtons.prop('disabled', true);
    
    // Show loading state
    buttonElement.html('<i class="fa fa-spinner fa-spin"></i> Processing...');
    
    // Make AJAX request
    const ajaxUrl = window.approvalData.baseUrl + '/approval/approve';

    $.ajax({
        url: ajaxUrl,
        type: 'POST',
        data: {
            claimId: claimId,
            action: action
        },
        dataType: 'json',
        success: function(response) {
            
            if (response.success) {
                // Update the UI
                updateClaimStatus(row, action);

                // Show success message using jubiqAlert
                jubiqAlert('success', response.message || 'Claim ' + action.toLowerCase() + ' successfully');

                // Disable action buttons
                actionButtons.remove();
                row.find('.actions-cell').html('<span class="text-muted">Action completed</span>');

            } else {
                // Show error message using jubiqAlert
                jubiqAlert('danger', response.error || 'Failed to process approval');

                // Re-enable buttons
                resetButtonState(buttonElement, action);
                actionButtons.prop('disabled', false);
            }
        },
        error: function(xhr) {
            // Simplified error logging for production

            let errorMessage = 'Failed to process approval';

            // Handle different types of responses
            if (xhr.status === 404) {
                errorMessage = 'Approval action not found. Please check URL routing.';
            } else if (xhr.responseText) {
                // Check if response is HTML (PHP error page)
                if (xhr.responseText.includes('<html>') || xhr.responseText.includes('<!DOCTYPE')) {
                    errorMessage = 'Server error occurred. Please check server logs.';
                } else {
                    try {
                        const errorData = JSON.parse(xhr.responseText);
                        errorMessage = errorData.error || errorMessage;
                    } catch (e) {
                        errorMessage = xhr.responseText.substring(0, 200); // Limit error message length
                    }
                }
            }

            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }

            jubiqAlert('danger', errorMessage);

            // Re-enable buttons
            resetButtonState(buttonElement, action);
            actionButtons.prop('disabled', false);
        }
    });
}

/**
 * Reset button state after error
 */
function resetButtonState(buttonElement, action) {
    if (action === 'APPROVED') {
        buttonElement.html('<i class="fa fa-check"></i> Approve');
    } else {
        buttonElement.html('<i class="fa fa-times"></i> Not Approve');
    }
}

/**
 * Update claim status in the table
 */
function updateClaimStatus(row, action) {
    const statusCell = row.find('.approval-status-cell');
    const currentDate = new Date().toLocaleDateString('en-GB');
    
    let statusHtml = '';
    if (action === 'APPROVED') {
        statusHtml = '<span class="approval-status approved">APPROVED<br><small>' + currentDate + '</small></span>';
    } else {
        statusHtml = '<span class="approval-status not-approved">NOT_APPROVED<br><small>' + currentDate + '</small></span>';
    }
    
    statusCell.html(statusHtml);
}

/**
 * Show success/error messages using jubiqAlert (top-right corner)
 * This function is now handled by jubiqAlert() which is defined in the layout
 */

/**
 * Handle authentication errors
 */
function handleAuthError() {
    showMessage('Authentication expired. Please use a new link from your email.', 'error');
    
    // Disable all action buttons
    $('.approve-btn, .not-approve-btn').prop('disabled', true);
}

/**
 * Utility function to format dates
 */
function formatDate(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-GB') + ' ' + date.toLocaleTimeString('en-GB', { 
        hour: '2-digit', 
        minute: '2-digit' 
    });
}

/**
 * Debug function to log approval data
 */
function debugApprovalData() {
    if (typeof window.approvalData !== 'undefined') {
        // Debug data available - use browser dev tools to inspect window.approvalData
        return window.approvalData;
    } else {
        console.error('Approval data not available for debugging');
        return null;
    }
}

/**
 * Show robustness details in popup modal
 */
ApprovalPage.showRobustnessDetails = function(robustnessId) {
    if (!robustnessId) {
        jubiqAlert('danger', 'Invalid robustness ID');
        return;
    }

    // Make AJAX call to get robustness details via proxy endpoint
    var proxyUrl = window.approvalData.baseUrl + '/approval/getRobustnessDetails/' + robustnessId;

    $.ajax({
        url: proxyUrl,
        type: 'GET',
        dataType: 'json',
        success: function(result) {

            if (result && (result.itemName || result.id)) {
                var html = '<tr>';
                html += '<td>' + (result.itemName || 'N/A') + '</td>';
                html += '<td>' + (result.description || 'No description available') + '</td>';
                html += '</tr>';

                $('#robustness-details').html(html);
                $('#robustness-modal').modal('show');
            } else {
                jubiqAlert('warning', 'No robustness details found');
            }
        },
        error: function(xhr) {
            jubiqAlert('danger', 'Failed to load robustness details: ' + (xhr.responseText || 'Unknown error'));
        }
    });
};

/**
 * Show fine and penalty details in popup modal
 */
ApprovalPage.showFinePenaltyDetails = function(finePenaltyId) {
    if (!finePenaltyId) {
        jubiqAlert('danger', 'Invalid fine and penalty ID');
        return;
    }

    // Make AJAX call to get fine and penalty details via proxy endpoint
    var proxyUrl = window.approvalData.baseUrl + '/approval/getFinePenaltyDetails/' + finePenaltyId;

    $.ajax({
        url: proxyUrl,
        type: 'GET',
        dataType: 'json',
        success: function(result) {

            if (result && (result.itemName || result.id)) {
                var html = '<tr>';
                html += '<td>' + (result.itemName || 'N/A') + '</td>';
                html += '<td>' + (result.descriptionOfActs || 'N/A') + '</td>';
                html += '<td>' + (result.penaltiesSanctionsApplied || 'N/A') + '</td>';
                html += '<td>' + (result.otherRemedies || 'N/A') + '</td>';
                html += '<td>' + (result.legalBackground || 'N/A') + '</td>';
                html += '</tr>';

                $('#fine-penalty-details').html(html);
                $('#fine-penalty-modal').modal('show');
            } else {
                jubiqAlert('warning', 'No fine and penalty details found');
            }
        },
        error: function(xhr) {
            jubiqAlert('danger', 'Failed to load fine and penalty details: ' + (xhr.responseText || 'Unknown error'));
        }
    });
};

// Expose debug function globally for console use
window.debugApprovalData = debugApprovalData;
