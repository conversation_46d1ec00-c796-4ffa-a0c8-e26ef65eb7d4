/**
 * CRA Claims Management JavaScript
 * Optimized for performance and maintainability
 */

// Global state management
var CraClaimsModule = (function() {
    'use strict';

    // Private variables
    var claimsData = [];
    var robustnessCache = {};
    var finePenaltyCache = {};
    var isInitialized = false;

    // Configuration
    var config = {
        selectors: {
            claimsTable: '#claims-table tbody',
            claimForm: '#claim-form',
            claimTypeRadio: 'input[name="claimType"]',
            textGroup: '#claims-text-group',
            imageGroup: '#claims-image-group',
            textInput: '#claim-text',
            imageInput: '#claim-image'
        },
        classes: {
            robustnessLink: 'robustness-link',
            finePenaltyLink: 'fine-penalty-link',
            mktAcceptBtn: 'mkt-accept-claim',
            mktRejectBtn: 'mkt-reject-claim'
        }
    };

    /**
     * Initialize claims data from server
     */
    function initializeClaimsData(serverClaimsData) {
        if (!serverClaimsData || !Array.isArray(serverClaimsData)) {
            return;
        }

        claimsData = serverClaimsData;

        // Load reference data and update table
        loadReferenceData()
            .then(enrichClaimsDataWithNames)
            .then(updateClaimsTable)
            .catch(function() {
                updateClaimsTable();
            });
    }

    /**
     * Load reference data from window objects
     */
    function loadReferenceData() {
        // Load robustness data
        if (window.robustnessOptions && Array.isArray(window.robustnessOptions)) {
            window.robustnessOptions.forEach(function(item) {
                if (item && item.id) {
                    robustnessCache[item.id] = item;
                }
            });
        }

        // Load fine and penalty data
        if (window.finePenaltyOptions && Array.isArray(window.finePenaltyOptions)) {
            window.finePenaltyOptions.forEach(function(item) {
                if (item && item.id) {
                    finePenaltyCache[item.id] = item;
                }
            });
        }

        return $.Deferred().resolve().promise();
    }

    /**
     * Enrich claims data with reference names
     */
    function enrichClaimsDataWithNames() {
        claimsData.forEach(function(claim) {
            // Set robustness name from cache if not already provided
            if (claim.robustnessId && !claim.robustnessName) {
                var robustnessItem = robustnessCache[claim.robustnessId];
                if (robustnessItem) {
                    claim.robustnessName = robustnessItem.itemName;
                } else {
                    claim.robustnessName = 'Robustness (ID: ' + claim.robustnessId + ')';
                }
            }

            // Set fine and penalty name from cache if not already provided
            if (claim.fineAndPenaltyId && !claim.fineAndPenaltyName) {
                var finePenaltyItem = finePenaltyCache[claim.fineAndPenaltyId];
                if (finePenaltyItem) {
                    claim.fineAndPenaltyName = finePenaltyItem.itemName;
                } else {
                    claim.fineAndPenaltyName = 'Fine/Penalty (ID: ' + claim.fineAndPenaltyId + ')';
                }
            }
        });

        return $.Deferred().resolve().promise();
    }

    // Public API
    return {
        init: initializeClaimsData,
        getData: function() { return claimsData; },
        updateTable: updateClaimsTable,
        resetForm: resetClaimForm,
        getRobustnessCache: function() { return robustnessCache; },
        getFinePenaltyCache: function() { return finePenaltyCache; }
    };
})();

$(document).ready(function() {
    // Initialize claims table on page load
    updateClaimsTable();
    
    // Claim type radio button change handler
    $('input[name="claimType"]').change(function() {
        var selectedType = $(this).val();

        if (selectedType === 'text') {
            $('#claims-text-group').show();
            $('#claims-image-group').hide();
            $('#claim-text').prop('required', true);
            $('#claim-image').prop('required', false);
        } else if (selectedType === 'image') {
            $('#claims-text-group').hide();
            $('#claims-image-group').show();
            $('#claim-text').prop('required', false);
            $('#claim-image').prop('required', true);
        }
    });

    // Add claim button handler
    $('#add-claim').click(function(e) {
        e.preventDefault();

        var claimType = $('input[name="claimType"]:checked').val();
        var claimsContent = '';
        var isValid = true;
        
        // Validate based on claim type
        if (claimType === 'text') {
            claimsContent = $('#claim-text').val().trim();
            if (!claimsContent) {
                alert('Please enter claims text');
                isValid = false;
            }
        } else if (claimType === 'image') {
            var fileInput = $('#claim-image')[0];
            if (!fileInput.files || fileInput.files.length === 0) {
                alert('Please select an image file');
                isValid = false;
            } else {
                claimsContent = fileInput.files[0].name; // Store filename for now
            }
        }
        
        // Validate other required fields
        var detail = $('#claim-detail').val().trim();
        var robustnessId = $('#robustness').val();
        var robustnessText = $('#robustness option:selected').text(); // Capture selected text
        var framedRisk = $('#framed-risk').val();
        var criticalRisk = $('#critical-risk').val();
        var fineAndPenaltyId = $('#fine-penalty').val();
        var fineAndPenaltyText = $('#fine-penalty option:selected').text(); // Capture selected text
        
        if (!detail) {
            alert('Please enter detail');
            isValid = false;
        }
        if (!robustnessId) {
            alert('Please select robustness');
            isValid = false;
        }
        // Validate that at least one of Framed Risk or Critical Risk is selected
        if (!framedRisk && !criticalRisk) {
            alert('Please select either Framed Risk or Critical Risk');
            isValid = false;
        }
        if (!fineAndPenaltyId) {
            alert('Please select fine and penalty');
            isValid = false;
        }
        
        if (!isValid) {
            return;
        }

        // Check if editing existing claim
        var claimId = $('#claim-id').val();
        var claim;

        if (claimId) {
            // Update existing claim
            claim = claimsData.find(function(c) { return c.id == claimId; });
            if (claim) {
                claim.claimType = claimType;
                claim.claims = claimsContent;
                claim.detail = detail;
                claim.robustnessId = robustnessId ? parseInt(robustnessId, 10) : null;
                claim.robustnessName = robustnessId && robustnessText !== 'Select Robustness' ? robustnessText : null;
                claim.framedRisk = framedRisk;
                claim.criticalRisk = criticalRisk;
                claim.fineAndPenaltyId = fineAndPenaltyId ? parseInt(fineAndPenaltyId, 10) : null;
                claim.fineAndPenaltyName = fineAndPenaltyId && fineAndPenaltyText !== 'Select Fine and Penalty' ? fineAndPenaltyText : null;
            }
        } else {
            // Create new claim
            var craIdValue = $('#claim-form input[name="craId"]').val();

            // Alternative method to get CRA ID if form selector fails
            if (!craIdValue || craIdValue === 'NOT_SET') {
                craIdValue = $('input[name="craId"]').val();
            }

            // Convert craId to integer to ensure proper data type
            craIdValue = parseInt(craIdValue, 10);
            if (isNaN(craIdValue)) {
                console.error('Invalid CRA ID: not a number');
                alert('Error: Invalid CRA ID. Cannot save claim.');
                return;
            }

            claim = {
                id: Date.now(), // Temporary ID for client-side
                claimType: claimType,
                claims: claimsContent,
                detail: detail,
                robustnessId: robustnessId ? parseInt(robustnessId, 10) : null,
                robustnessName: robustnessId && robustnessText !== 'Select Robustness' ? robustnessText : null,
                framedRisk: framedRisk,
                criticalRisk: criticalRisk,
                fineAndPenaltyId: fineAndPenaltyId ? parseInt(fineAndPenaltyId, 10) : null,
                fineAndPenaltyName: fineAndPenaltyId && fineAndPenaltyText !== 'Select Fine and Penalty' ? fineAndPenaltyText : null,
                craId: craIdValue
            };

            // Add to claims array
            claimsData.push(claim);
        }
        
        // Update the claims table
        updateClaimsTable();
        
        // Reset form
        resetClaimForm();
    });

    // Cancel claim form
    $('#cancel-claim').click(function() {
        resetClaimForm();
    });

    // Edit claim
    $(document).on('click', '.edit-claim', function() {
        var claimId = $(this).data('claim-id');

        // Find claim in claimsData array
        var claim = claimsData.find(function(c) { return c.id == claimId; });
        if (!claim) {
            return;
        }
        
        // Populate form with existing data
        $('#claim-id').val(claimId);
        
        // Set claim type
        $('input[name="claimType"][value="' + claim.claimType + '"]').prop('checked', true);
        $('input[name="claimType"]').trigger('change');
        
        // Set claims content
        if (claim.claimType === 'text') {
            $('#claim-text').val(claim.claims);
        }
        
        // Set other fields
        $('#claim-detail').val(claim.detail);
        $('#robustness').val(claim.robustnessId);
        $('#framed-risk').val(claim.framedRisk);
        $('#critical-risk').val(claim.criticalRisk);
        $('#fine-penalty').val(claim.fineAndPenaltyId);
        
        // Scroll to form
        $('html, body').animate({
            scrollTop: $('#claim-form').offset().top
        }, 500);
    });

    // Remove claim
    $(document).on('click', '.remove-claim', function() {
        var claimId = $(this).data('claim-id');

        if (confirm('Are you sure you want to remove this claim?')) {
            // Remove from claimsData array
            claimsData = claimsData.filter(function(c) { return c.id != claimId; });
            
            // Update table
            updateClaimsTable();
            
            // Reset form if editing this claim
            if ($('#claim-id').val() == claimId) {
                resetClaimForm();
            }
        }
    });

    // Image file preview
    $('#claim-image').change(function() {
        var file = this.files[0];
        var preview = $('#claim-image-preview');
        preview.empty();
        
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                preview.html('<img src="' + e.target.result + '" style="max-width: 200px; max-height: 200px;" class="img-thumbnail">');
            };
            reader.readAsDataURL(file);
        }
    });

    // Robustness link click handler
    $(document).on('click', '.robustness-link', function(e) {
        e.preventDefault();
        var robustnessId = $(this).data('robustness-id');
        showRobustnessDetails(robustnessId);
    });

    // Fine and Penalty link click handler
    $(document).on('click', '.fine-penalty-link', function(e) {
        e.preventDefault();
        var finePenaltyId = $(this).data('fine-penalty-id');
        showFinePenaltyDetails(finePenaltyId);
    });

    // Marketing acceptance button handlers
    $(document).on('click', '.mkt-accept-claim, .mkt-reject-claim', function(e) {
        e.preventDefault();
        var claimId = $(this).data('claim-id');
        var action = $(this).data('action');
        handleMarketingAcceptance(claimId, action);
    });
});

// Legacy support - these functions now delegate to the module
function loadReferenceData() {
    return CraClaimsModule.loadReferenceData ? CraClaimsModule.loadReferenceData() : $.Deferred().resolve().promise();
}

function enrichClaimsDataWithNames() {
    return CraClaimsModule.enrichClaimsDataWithNames ? CraClaimsModule.enrichClaimsDataWithNames() : $.Deferred().resolve().promise();
}

/**
 * Function to reset claim form
 */
function resetClaimForm() {
    // Check if form exists before trying to reset
    var claimForm = $('#claim-form');
    if (claimForm.length > 0 && claimForm[0]) {
        claimForm[0].reset();
    }

    // Reset individual fields safely
    $('#claim-id').val('');
    $('#claims-text-group').show();
    $('#claims-image-group').hide();
    $('#claim-text').prop('required', true);
    $('#claim-image').prop('required', false);
    $('#claim-image-preview').empty();
    $('input[name="claimType"][value="text"]').prop('checked', true);
}

/**
 * Optimized function to update claims table
 */
function updateClaimsTable() {
    var tbody = $('#claims-list');

    if (!claimsData || claimsData.length === 0) {
        tbody.html('<tr><td colspan="9" class="text-center">No claims added yet</td></tr>');
        return;
    }

    // Build all rows in memory first for better performance
    var rows = claimsData.map(function(claim) {
        return buildClaimRow(claim);
    });

    // Update DOM once
    tbody.html(rows.join(''));
}

/**
 * Build a single claim row HTML
 */
function buildClaimRow(claim) {
    var robustnessCache = CraClaimsModule.getRobustnessCache();
    var finePenaltyCache = CraClaimsModule.getFinePenaltyCache();

    var row = '<tr data-claim-id="' + claim.id + '">';

    // Claims text
    row += '<td class="claim-text">' + escapeHtml(claim.claims || '') + '</td>';

    // Robustness column
    row += '<td>' + buildRobustnessCell(claim, robustnessCache) + '</td>';

    // Detail column
    row += '<td class="claim-detail">' + escapeHtml(claim.detail || '') + '</td>';

    // Framed Risk column
    row += '<td>' + buildRiskBadge(claim.framedRisk, '#6c757d') + '</td>';

    // Critical Risk column
    row += '<td>' + buildRiskBadge(claim.criticalRisk, '#6f42c1') + '</td>';
            row += '<span class="badge" style="background-color: #6f42c1;">' + claim.criticalRisk + '</span>';
        }
        row += '</td>';
        // Fine and Penalty column with enhanced display and debugging
        row += '<td>';
        if (claim.fineAndPenaltyId) {
            // Prioritize backend-enriched name, then cache lookup, then fallback
            var finePenaltyText = claim.fineAndPenaltyName;
            if (!finePenaltyText && finePenaltyCache[claim.fineAndPenaltyId]) {
                finePenaltyText = finePenaltyCache[claim.fineAndPenaltyId].itemName;
            }

            if (!finePenaltyText) {
                finePenaltyText = 'Fine/Penalty (ID: ' + claim.fineAndPenaltyId + ')';
            }


            row += '<a href="#" class="fine-penalty-link" data-fine-penalty-id="' + claim.fineAndPenaltyId + '">' + finePenaltyText + '</a>';
        }
        row += '</td>';

        // MKT Accepted status
        row += '<td class="mkt-accepted">';
        if (claim.mktAcceptedStatus && claim.mktAcceptedDate) {
            var mktDate = new Date(claim.mktAcceptedDate);
            var status = claim.mktAcceptedStatus.toLowerCase();
            var displayText = status === 'accepted' ? 'Accepted' : 'Not Accepted';
            row += displayText + ' (' + mktDate.toLocaleDateString() + ')';
        } else {
            row += '';
        }
        row += '</td>';

        // Approval status
        row += '<td class="approval-status">';
        if (claim.approvalStatus && claim.approvalDate) {
            var approvalDate = new Date(claim.approvalDate);
            var displayStatus = formatApprovalStatus(claim.approvalStatus);
            row += displayStatus + ' (' + approvalDate.toLocaleDateString() + ')';
        } else {
            row += '';
        }
        row += '</td>';

        // Actions column with role-based buttons
        row += '<td>';
        row += getActionButtonsForClaim(claim);
        row += '</td>';
        row += '</tr>';
        
        tbody.append(row);
    });
}

/**
 * Get role-based action buttons for a claim
 */
function getActionButtonsForClaim(claim) {
    var buttons = '';
    var userGroup = window.userGroupName || '';
    var currentPage = window.currentPage || '';

    // SCI Groups on process page: Edit and Remove buttons
    if ((userGroup === 'SCI Manager' || userGroup === 'SCI Staff') && currentPage === 'process') {
        buttons += '<button class="btn btn-xs btn-primary edit-claim" data-claim-id="' + claim.id + '">Edit</button> ';
        buttons += '<button class="btn btn-xs btn-danger remove-claim" data-claim-id="' + claim.id + '">Remove</button>';
    }
    // Marketing Groups on update page: Accepted and Not Accepted buttons
    else if (['Marketing', 'CPD', 'ACD', 'PPD', 'LUXE'].indexOf(userGroup) !== -1 && currentPage === 'update') {
        if (!claim.mktAcceptedStatus) {
            buttons += '<button class="btn btn-xs btn-success mkt-accept-claim" data-claim-id="' + claim.id + '" data-action="accepted">Accepted</button> ';
            buttons += '<button class="btn btn-xs btn-warning mkt-reject-claim" data-claim-id="' + claim.id + '" data-action="not_accepted">Not Accepted</button>';
        } else {
            // Show current status if already decided (handle both uppercase and lowercase)
            var status = claim.mktAcceptedStatus.toLowerCase();
            var statusClass = status === 'accepted' ? 'btn-success' : 'btn-warning';
            var displayText = status === 'accepted' ? 'Accepted' : 'Not Accepted';
            buttons += '<span class="btn btn-xs ' + statusClass + ' disabled">' + displayText + '</span>';
        }
    }
    // Default: Edit and Remove for backward compatibility
    else {
        buttons += '<button class="btn btn-xs btn-primary edit-claim" data-claim-id="' + claim.id + '">Edit</button> ';
        buttons += '<button class="btn btn-xs btn-danger remove-claim" data-claim-id="' + claim.id + '">Remove</button>';
    }

    return buttons;
}

/**
 * Function to collect claims data for form submission
 */
function getClaimsDataJson() {
    return JSON.stringify(claimsData);
}

/**
 * Show robustness details in popup modal using cached data or backend-enriched data
 */
function showRobustnessDetails(robustnessId) {
    // Validate robustness ID
    if (!robustnessId || isNaN(robustnessId)) {
        alert('Invalid robustness ID: ' + robustnessId);
        return;
    }



    // Get data from cache first
    var data = robustnessCache[robustnessId];

    // If not in cache, try to find it in the claims data (backend-enriched)
    if (!data) {
        var claimWithRobustness = claimsData.find(function(claim) {
            return claim.robustnessId == robustnessId && claim.robustnessName;
        });
        if (claimWithRobustness) {
            data = {
                id: robustnessId,
                itemName: claimWithRobustness.robustnessName,
                description: 'Details available to SCI team only'
            };
        }
    }

    if (data) {
        var modalHtml = '<div class="modal fade" id="robustnessModal" tabindex="-1" role="dialog" aria-labelledby="robustnessModalLabel">' +
            '<div class="modal-dialog" role="document">' +
            '<div class="modal-content">' +
            '<div class="modal-header">' +
            '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span>' +
            '</button>' +
            '<h4 class="modal-title" id="robustnessModalLabel">Robustness Details</h4>' +
            '</div>' +
            '<div class="modal-body">' +
            '<table class="table table-bordered">' +
            '<tr><td><strong>ID:</strong></td><td>' + robustnessId + '</td></tr>' +
            '<tr><td><strong>Item Name:</strong></td><td>' + (data.itemName || 'N/A') + '</td></tr>' +
            '<tr><td><strong>Description:</strong></td><td>' + (data.description || 'N/A') + '</td></tr>' +
            '</table>' +
            '</div>' +
            '<div class="modal-footer">' +
            '<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>' +
            '</div>' +
            '</div></div></div>';

        // Remove existing modal and add new one
        $('#robustnessModal').remove();
        $('body').append(modalHtml);
        $('#robustnessModal').modal('show');
    } else {


        // Show a user-friendly modal indicating data is not available
        var modalHtml = '<div class="modal fade" id="robustnessModal" tabindex="-1" role="dialog" aria-labelledby="robustnessModalLabel">' +
            '<div class="modal-dialog" role="document">' +
            '<div class="modal-content">' +
            '<div class="modal-header">' +
            '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span>' +
            '</button>' +
            '<h4 class="modal-title" id="robustnessModalLabel">Robustness Details</h4>' +
            '</div>' +
            '<div class="modal-body">' +
            '<table class="table table-bordered">' +
            '<tr><td><strong>ID:</strong></td><td>' + robustnessId + '</td></tr>' +
            '<tr><td><strong>Status:</strong></td><td>Robustness details are currently being loaded</td></tr>' +
            '<tr><td><strong>Note:</strong></td><td>Please refresh the page or contact support if this issue persists</td></tr>' +
            '</table>' +
            '</div>' +
            '<div class="modal-footer">' +
            '<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>' +
            '</div>' +
            '</div></div></div>';

        // Remove existing modal and add new one
        $('#robustnessModal').remove();
        $('body').append(modalHtml);
        $('#robustnessModal').modal('show');
    }
}

/**
 * Show fine and penalty details in popup modal using cached data or backend-enriched data
 */
function showFinePenaltyDetails(finePenaltyId) {
    // Validate fine and penalty ID
    if (!finePenaltyId || isNaN(finePenaltyId)) {
        alert('Invalid fine and penalty ID: ' + finePenaltyId);
        return;
    }



    // Get data from cache first
    var data = finePenaltyCache[finePenaltyId];

    // If not in cache, try to find it in the claims data (backend-enriched)
    if (!data) {
        var claimWithFinePenalty = claimsData.find(function(claim) {
            return claim.fineAndPenaltyId == finePenaltyId && claim.fineAndPenaltyName;
        });
        if (claimWithFinePenalty) {
            data = {
                id: finePenaltyId,
                itemName: claimWithFinePenalty.fineAndPenaltyName,
                descriptionOfActs: 'Details available to SCI team only',
                penaltiesSanctionsApplied: 'Details available to SCI team only',
                otherRemedies: 'Details available to SCI team only',
                legalBackground: 'Details available to SCI team only'
            };
        }
    }

    if (data) {
        var modalHtml = '<div class="modal fade" id="finePenaltyModal" tabindex="-1" role="dialog" aria-labelledby="finePenaltyModalLabel">' +
            '<div class="modal-dialog modal-lg" role="document">' +
            '<div class="modal-content">' +
            '<div class="modal-header">' +
            '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span>' +
            '</button>' +
            '<h4 class="modal-title" id="finePenaltyModalLabel">Fine and Penalty Details</h4>' +
            '</div>' +
            '<div class="modal-body">' +
            '<table class="table table-bordered">' +
            '<tr><td><strong>ID:</strong></td><td>' + finePenaltyId + '</td></tr>' +
            '<tr><td><strong>Item Name:</strong></td><td>' + (data.itemName || 'N/A') + '</td></tr>' +
            '<tr><td><strong>Description of Acts:</strong></td><td>' + (data.descriptionOfActs || 'N/A') + '</td></tr>' +
            '<tr><td><strong>Penalties/Sanctions Applied:</strong></td><td>' + (data.penaltiesSanctionsApplied || 'N/A') + '</td></tr>' +
            '<tr><td><strong>Other Remedies:</strong></td><td>' + (data.otherRemedies || 'N/A') + '</td></tr>' +
            '<tr><td><strong>Legal Background:</strong></td><td>' + (data.legalBackground || 'N/A') + '</td></tr>' +
            '</table>' +
            '</div>' +
            '<div class="modal-footer">' +
            '<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>' +
            '</div>' +
            '</div></div></div>';

        // Remove existing modal and add new one
        $('#finePenaltyModal').remove();
        $('body').append(modalHtml);
        $('#finePenaltyModal').modal('show');
    } else {


        // Show a user-friendly modal indicating data is not available
        var modalHtml = '<div class="modal fade" id="finePenaltyModal" tabindex="-1" role="dialog" aria-labelledby="finePenaltyModalLabel">' +
            '<div class="modal-dialog modal-lg" role="document">' +
            '<div class="modal-content">' +
            '<div class="modal-header">' +
            '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span>' +
            '</button>' +
            '<h4 class="modal-title" id="finePenaltyModalLabel">Fine and Penalty Details</h4>' +
            '</div>' +
            '<div class="modal-body">' +
            '<table class="table table-bordered">' +
            '<tr><td><strong>ID:</strong></td><td>' + finePenaltyId + '</td></tr>' +
            '<tr><td><strong>Status:</strong></td><td>Fine and penalty details are currently being loaded</td></tr>' +
            '<tr><td><strong>Note:</strong></td><td>Please refresh the page or contact support if this issue persists</td></tr>' +
            '</table>' +
            '</div>' +
            '<div class="modal-footer">' +
            '<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>' +
            '</div>' +
            '</div></div></div>';

        // Remove existing modal and add new one
        $('#finePenaltyModal').remove();
        $('body').append(modalHtml);
        $('#finePenaltyModal').modal('show');
    }
}

/**
 * Handle marketing acceptance/rejection of claims
 */
function handleMarketingAcceptance(claimId, action) {
    if (!confirm('Are you sure you want to ' + action.replace('_', ' ') + ' this claim?')) {
        return;
    }

    // Get the claim data to determine risk level
    var claim = claimsData.find(function(c) { return c.id == claimId; });
    if (!claim) {
        alert('Claim not found');
        return;
    }



    // Proceed with the API call via PHP proxy
    performMktAcceptCall(claimId, action, claim);
}

/**
 * Note: API connectivity test removed since PHP proxy is working correctly
 */

/**
 * Perform the actual mkt-accept API call via PHP proxy
 */
function performMktAcceptCall(claimId, action, claim) {
    $.ajax({
        url: baseUrl + '/cra/apiMktAccept/' + claimId,
        type: 'POST',
        data: {
            mktAcceptedStatus: action.toUpperCase()
        },
        success: function() {
            handleSuccessfulMktAccept(claimId, action, claim);
        },
        error: function(xhr, status, error) {
            console.error('Error updating claim acceptance status:', error);

            // Try to parse error response
            try {
                var errorResponse = JSON.parse(xhr.responseText);
                alert('Error updating claim acceptance status: ' + (errorResponse.error || error));
            } catch (e) {
                alert('Error updating claim acceptance status: ' + error + ' (Status: ' + xhr.status + ')');
            }
        }
    });
}

/**
 * Note: Alternative API call methods removed since we're now using PHP proxy
 * which handles the backend communication directly.
 */

/**
 * Handle successful marketing acceptance (common logic)
 */
function handleSuccessfulMktAccept(claimId, action, claim) {
    // Update the claim in claimsData
    if (claim) {
        claim.mktAcceptedStatus = action.toUpperCase();
        claim.mktAcceptedDate = new Date().toISOString();
        claim.mktAcceptedBy = window.userId || null;
    }

    // Step 2: If action is "accepted", call approve endpoint with risk-based logic
    if (action.toLowerCase() === 'accepted') {
        handleApprovalWorkflow(claimId, claim);
    } else {
        // For "not_accepted", only step 1 is needed
        updateClaimsTable();
    }
}

/**
 * Handle the approval workflow based on risk assessment
 */
function handleApprovalWorkflow(claimId, claim) {
    // Determine approval status and approver type based on risk level
    var approvalStatus;
    var approverType = window.userGroupName || 'Marketing'; // Default to Marketing if not available

    // Risk level determination logic
    if (claim.framedRisk && !claim.criticalRisk) {
        // Framed Risk claims
        approvalStatus = 'APPROVED';
    } else if (claim.criticalRisk) {
        // Critical Risk claims - check specific levels
        var criticalLevel = claim.criticalRisk.toUpperCase();
        if (criticalLevel === 'IIIC' || criticalLevel === 'ID' || criticalLevel === 'IID') {
            approvalStatus = 'PENDING_GM';
        } else if (criticalLevel === 'IIID') {
            approvalStatus = 'PENDING_CM';
        } else {
            // Default for other critical risk levels
            approvalStatus = 'PENDING_GM';
        }
    } else {
        // No risk assessment available - default to approved
        approvalStatus = 'APPROVED';
    }

    // Step 2: Call approve endpoint via PHP proxy
    $.ajax({
        url: baseUrl + '/cra/apiApprove/' + claimId,
        type: 'POST',
        data: {
            approvalStatus: approvalStatus,
            approverType: approverType
        },
        success: function() {
            // Update the claim in claimsData
            claim.approvalStatus = approvalStatus;
            claim.approvalDate = new Date().toISOString();
            claim.approverId = window.userId || null;
            claim.approverType = approverType;

            // Refresh the table
            updateClaimsTable();
        },
        error: function(xhr, status, error) {
            console.error('Error in approval workflow:', error);

            // Try to parse error response
            try {
                var errorResponse = JSON.parse(xhr.responseText);
                alert('Claim was accepted but approval workflow failed: ' + (errorResponse.error || error));
            } catch (e) {
                alert('Claim was accepted but approval workflow failed: ' + error);
            }

            // Still refresh the table since step 1 succeeded
            updateClaimsTable();
        }
    });
}

/**
 * Format approval status for display
 */
function formatApprovalStatus(status) {
    if (!status) return '';

    switch (status.toUpperCase()) {
        case 'APPROVED':
            return 'Approved';
        case 'PENDING_GM':
            return 'Pending GM';
        case 'PENDING_CM':
            return 'Pending CM';
        case 'NOT_APPROVED':
            return 'Not Approved';
        default:
            return status;
    }
}

/**
 * Note: API testing functions removed to reduce console clutter
 */



// Expose functions globally for main form submission and external access
window.getClaimsDataJson = getClaimsDataJson;
window.initializeClaimsData = initializeClaimsData;
window.updateClaimsTable = updateClaimsTable;
window.resetClaimForm = resetClaimForm;
