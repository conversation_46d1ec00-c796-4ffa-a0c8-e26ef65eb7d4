/**
 * Custom styles for the headerless approval page layout
 */

/* Body and Layout */
.approval-page {
    background-color: #ffffff;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.approval-app {
    min-height: 100vh;
    background-color: #f6f7f8;
    padding-bottom: 40px;
}

/* Container fluid overrides for compact layout (except header) */
.approval-container.container-fluid {
    padding-left: 0;
    padding-right: 0;
    overflow-x: visible;
}

/* Row spacing optimization - Apply only to content rows, not header */
.approval-container .row {
    margin-left: -10px;
    margin-right: -10px;
}

.approval-container .row > [class*="col-"] {
    padding-left: 10px;
    padding-right: 10px;
}

.approval-content {
    padding: 0;
    background-color: #f6f7f8;
}

.approval-container {
    max-width: none;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header Section - Full width */
.approval-header {
    background-color: #155abb;
    color: #d7e5f8;
    margin-bottom: 45px;
    padding: 15px 0; 
    font-family: "PT Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    width: 100%;
    border-top: 0;
    font-size: 14px;
}

/* Header container to control inner content padding */
.approval-header .container-fluid {
    padding-left: 20px;
    padding-right: 20px;
}

.approval-title {
    color: #d7e5f8;
    font-size: 28px;
    font-weight: bold;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: "PT Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.approval-user-info {
    color: #d7e5f8;
    font-size: 14px;
    margin: 0;
    padding-top: 8px;
    font-family: "PT Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* Responsive header adjustments */
@media (max-width: 768px) {
    .approval-header {
        padding: 12px 0;
        text-align: center;
        margin-bottom: 10px;
    }

    .approval-header .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }

    .approval-header .col-md-6 {
        text-align: center !important;
    }

    .approval-title {
        font-size: 24px;
        margin-bottom: 8px;
    }

    .approval-user-info {
        padding-top: 0;
        font-size: 13px;
    }

    .approval-container {
        padding: 0 10px;
    }
}

/* Panel Styling */
.approval-panel {
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.approval-panel .panel-body {
    padding: 0;
}

/* Table Styling */
.approval-table {
    margin-bottom: 0;
    font-size: 14px;
    border-collapse: collapse;
}

.approval-table th,
.approval-table td {
    border: 1px solid #ddd !important;
    padding: 8px 6px;
    text-align: center;
    vertical-align: middle;
    word-wrap: break-word;
    max-width: 120px;
}

/* Header Cells */
.approval-header-cell {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #333;
    text-transform: uppercase;
    font-size: 11px;
    padding: 10px 6px;
    border: 1px solid #ddd !important;
}

/* Critical Risk Header - Purple Background */
.critical-risk-header {
    background-color: #6f42c1 !important;
    color: white !important;
}

/* Data Cells */
.approval-cell {
    background-color: #ffffff;
    font-size: 11px;
    line-height: 1.3;
    padding: 6px 4px;
}

/* Critical Risk Cell */
.critical-risk-cell {
    text-align: center;
}

.critical-risk-badge {
    background-color: #6f42c1 !important;
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
}

/* Approval Status Styling */
.approval-status-cell {
    text-align: center;
}

.approval-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
}

.approval-status.approved {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.approval-status.not-approved,
.approval-status.not_approved {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.approval-status.pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Action Buttons */
.actions-cell {
    text-align: center;
    white-space: nowrap;
    min-width: 140px;
}

.approve-btn {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
    font-size: 10px;
    padding: 4px 8px;
    margin: 1px;
    border-radius: 3px;
}

.approve-btn:hover {
    background-color: #218838;
    border-color: #1e7e34;
    color: white;
}

.not-approve-btn {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    font-size: 10px;
    padding: 4px 8px;
    margin: 1px;
    border-radius: 3px;
}

.not-approve-btn:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: white;
}

.approve-btn:disabled,
.not-approve-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Table Hover Effects */
.table-row-hover {
    background-color: #f8f9fa !important;
}

.approval-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* No Claims Message */
.no-claims-message {
    padding: 40px 20px;
    text-align: center;
    color: #6c757d;
    font-size: 16px;
}

.no-claims-message i {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
}

/* Style Panel and Table Styling */
.panel-default {
    border: 1px solid #ddd;
    border-bottom: none;
    border-radius: 4px;
}

/* Request Information Table Styling */
.approval-info-table {
    margin-bottom: 0;
    font-size: 14px;
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
}

.approval-info-table td {
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 12px 15px;
    font-size: 14px;
}

.info-label {
    background-color: #f8f9fa;
    font-weight: bold;
    width: 200px;
    min-width: 200px;
    max-width: 200px;
    color: #777777;
}

.info-value {
    background-color: #ffffff;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    width: calc(100% - 200px);
    line-height: 1.4;
}

/* Responsive styling for information table */
@media (max-width: 768px) {
    .info-label {
        width: 120px;
        min-width: 120px;
        max-width: 120px;
        font-size: 12px;
        padding: 8px 10px;
    }

    .info-value {
        font-size: 12px;
        padding: 8px 10px;
        width: calc(100% - 120px);
        line-height: 1.3;
    }

    .approval-info-table td {
        padding: 8px 10px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .info-label {
        width: 100px; /* Fixed width for mobile */
        min-width: 100px; /* Prevent shrinking */
        max-width: 100px; /* Prevent expanding */
        font-size: 11px;
        padding: 6px 8px;
    }

    .info-value {
        font-size: 11px;
        padding: 6px 8px;
        width: calc(100% - 100px); /* Take remaining space */
        line-height: 1.2;
    }

    .approval-info-table td {
        padding: 6px 8px;
        font-size: 11px;
    }
}

.panel-heading {
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
    padding: 8px 15px;
}

.panel-heading h4 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.panel-body {
    padding: 25px 15px;
    overflow-x: auto;
}

/* Table Responsive Container */
.table-responsive {
    overflow-x: auto;
    min-height: 0.01%;
}

/* Approval Table Specific Styling */
.approval-table {
    font-size: 13px;
    margin-bottom: 0;
    min-width: 1200px; /* Reduced from 1400px due to fewer columns */
    table-layout: fixed;
}

.approval-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    text-align: center;
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 12px 8px;
    font-size: 14px;
    color: #777777;
    white-space: normal; /* Allow text wrapping on smaller screens */
    word-wrap: break-word;
    line-height: 1.2;
}

.approval-table td {
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 8px;
    font-size: 14px;
    overflow: hidden;
}

.approval-cell {
    word-wrap: break-word;
    overflow-wrap: break-word;
    text-overflow: ellipsis;
    position: relative;
}

/* Override ellipsis for actions cell to prevent button text truncation */
.actions-cell {
    text-overflow: clip !important;
    overflow: visible !important;
}

.approval-header-cell {
    background-color: #f8f9fa !important;
    font-weight: bold;
    text-align: center;
    white-space: nowrap;
}

.critical-risk-header {
    background-color: #6f42c1 !important;
    color: white !important;
}

.critical-risk-cell {
    text-align: center;
}

.critical-risk-badge {
    background-color: #6f42c1;
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
}

.approval-status-cell {
    text-align: center;
}

.approval-status {
    font-size: 11px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
}

/* Column Width Controls - Updated for 9-column layout */
.approval-table th:nth-child(1), .approval-table td:nth-child(1) { width: 120px; } /* Last Update */
.approval-table th:nth-child(2), .approval-table td:nth-child(2) { width: 120px; } /* Exposition */
.approval-table th:nth-child(3), .approval-table td:nth-child(3) { width: 250px; } /* Claims */
.approval-table th:nth-child(4), .approval-table td:nth-child(4) { width: 140px; } /* Robustness */
.approval-table th:nth-child(5), .approval-table td:nth-child(5) { width: 160px; } /* Detail */
.approval-table th:nth-child(6), .approval-table td:nth-child(6) { width: 120px; } /* Critical Risk */
.approval-table th:nth-child(7), .approval-table td:nth-child(7) { width: 140px; } /* Fine and Penalty */
.approval-table th:nth-child(8), .approval-table td:nth-child(8) { width: 120px; } /* Approval */
.approval-table th:nth-child(9), .approval-table td:nth-child(9) { width: 220px; } /* Actions */

.actions-cell {
    text-align: center;
    min-width: 200px;
    width: 200px !important;
    white-space: normal; /* Allow wrapping */
    line-height: 1.2;
}

/* Action Buttons Styling */
.actions-cell .btn {
    margin: 2px;
    padding: 4px 8px;
    font-size: 11px;
    white-space: nowrap;
    display: inline-block;
    text-overflow: clip;
    overflow: visible;
}

.actions-cell .approve-btn {
    min-width: 80px;
}

.actions-cell .not-approve-btn {
    min-width: 90px;
}

/* Ensure buttons never get truncated with ellipsis */
.actions-cell .btn {
    text-overflow: clip !important;
    overflow: visible !important;
    max-width: none !important;
}

/* Responsive adjustments for Actions column button stacking */
@media (max-width: 1400px) {
    /* When table starts to get cramped, stack buttons vertically */
    .actions-cell {
        white-space: normal;
        width: 120px !important;
        min-width: 120px;
    }

    .approval-table th {
        white-space: normal; /* Allow header text wrapping */
        word-wrap: break-word;
        line-height: 1.2;
    }

    .actions-cell .btn {
        display: block;
        width: calc(100% - 4px); /* Full width minus margins */
        margin: 1px 2px;
        font-size: 10px;
        padding: 3px 6px;
        text-overflow: clip;
        overflow: visible;
        white-space: nowrap;
    }

    .approval-table th:nth-child(9), .approval-table td:nth-child(9) {
        width: 120px !important;
    }
}

@media (max-width: 1200px) {
    /* Further optimize for medium screens */
    .actions-cell {
        width: 110px !important;
        min-width: 110px;
    }

    .actions-cell .btn {
        font-size: 9px;
        padding: 2px 4px;
        margin: 1px;
        width: calc(100% - 2px);
    }

    .approval-table th:nth-child(9), .approval-table td:nth-child(9) {
        width: 110px !important;
    }
}

@media (max-width: 768px) {
    .approval-table {
        min-width: 1200px;
        font-size: 11px;
    }

    .approval-table th,
    .approval-table td {
        padding: 6px 4px;
        font-size: 10px;
    }

    .approval-table th {
        white-space: normal;
        word-wrap: break-word;
        line-height: 1.1;
        min-height: 40px;
    }

    .actions-cell {
        width: 100px !important;
        min-width: 100px;
    }

    .actions-cell .btn {
        font-size: 8px;
        padding: 2px 3px;
        margin: 1px 0;
        width: 100%;
    }

    .approval-table th:nth-child(9), .approval-table td:nth-child(9) {
        width: 100px !important;
    }
}

.table-row-hover {
    background-color: #f8f9fa !important;
}

.approval-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Clickable Links for Robustness and Fine Penalty */
.robustness-link,
.fine-penalty-link {
    color: #337ab7;
    text-decoration: none;
    cursor: pointer;
}

.robustness-link:hover,
.fine-penalty-link:hover {
    color: #23527c;
    text-decoration: underline;
}

.robustness-link:focus,
.fine-penalty-link:focus {
    color: #23527c;
    text-decoration: underline;
    outline: none;
}

/* Notification Alert - Top-right corner positioning */
#jubiq-alert {
    position: fixed;
    top: 60px;
    right: 10px;
    z-index: 9999;
    width: 400px;
}

/* Legacy approval messages */
.approval-messages {
    margin: 20px 0;
}

.approval-alert {
    border-radius: 4px;
    padding: 12px 15px;
    margin-bottom: 15px;
}

.approval-alert i {
    margin-right: 8px;
}

/* Footer Styling */
.approval-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #f8f9fa;
    border-top: 1px solid #e7e7e7;
    z-index: 1000;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .approval-table {
        font-size: 10px;
    }
    
    .approval-table th,
    .approval-table td {
        padding: 4px 3px;
        max-width: 100px;
    }
    
    .approval-header-cell {
        font-size: 9px;
        padding: 8px 3px;
    }
    
    .approve-btn,
    .not-approve-btn {
        font-size: 9px;
        padding: 3px 6px;
    }
}

@media (max-width: 768px) {
    .approval-container {
        padding: 10px;
    }
    
    .approval-table {
        font-size: 9px;
    }
    
    .approval-table th,
    .approval-table td {
        padding: 3px 2px;
        max-width: 80px;
    }
    
    .actions-cell {
        min-width: 100px;
    }
    
    .approve-btn,
    .not-approve-btn {
        font-size: 8px;
        padding: 2px 4px;
        margin: 0.5px;
    }
    
    .approval-header h2 {
        font-size: 22px;
    }
}

/* Print Styles */
@media print {
    .approval-footer,
    .actions-cell,
    .approval-messages {
        display: none !important;
    }
    
    .approval-table {
        font-size: 10px;
    }
    
    .approval-app {
        padding-bottom: 0;
    }
}
