<?php
/**
 * Service class for Approval Page functionality
 * Handles magic login authentication and CRA claims approval
 */
class ApprovalService
{
    // Base API URL
    public static $baseUrl = "http://localhost:3005";
    
    /**
     * Authenticate user via quick login API
     * @param int $craRequestId
     * @param string $loginHash
     * @param string $email
     * @return array Authentication result
     */
    public static function authenticateQuickLogin($craRequestId, $loginHash, $email)
    {
        try {
            $url = self::$baseUrl . "/api/cra-quick-login/authenticate";
            
            $data = array(
                'craRequestId' => (int)$craRequestId,
                'loginHash' => $loginHash,
                'email' => $email
            );
            
            Yii::log("Quick login authentication - URL: {$url}", CLogger::LEVEL_INFO, 'approval');
            Yii::log("Quick login authentication - Data: " . print_r($data, true), CLogger::LEVEL_INFO, 'approval');
            
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_POST, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/x-www-form-urlencoded"
            ));
            
            $response = curl_exec($curl);
            $httpStatus = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);
            
            Yii::log("Quick login response - Status: {$httpStatus}, Response: {$response}", CLogger::LEVEL_INFO, 'approval');
            
            if ($httpStatus == 200) {
                $result = json_decode($response, true);
                return array('success' => true, 'data' => $result);
            } else {
                $errorMessage = 'Authentication failed';

                // Parse error response for more specific error messages
                if ($httpStatus == 401) {
                    $errorMessage = 'Invalid login credentials or expired link';
                } elseif ($httpStatus == 404) {
                    $errorMessage = 'CRA request not found or login link is invalid';
                } elseif ($httpStatus == 403) {
                    $errorMessage = 'Access denied - insufficient permissions';
                } elseif ($httpStatus >= 500) {
                    $errorMessage = 'Authentication service temporarily unavailable';
                } else {
                    $errorData = json_decode($response, true);
                    if ($errorData && isset($errorData['message'])) {
                        $errorMessage = $errorData['message'];
                    } elseif (is_string($response) && !empty($response)) {
                        $errorMessage = $response;
                    }
                }

                return array('success' => false, 'error' => $errorMessage);
            }
            
        } catch (Exception $e) {
            Yii::log("Quick login exception: " . $e->getMessage(), CLogger::LEVEL_ERROR, 'approval');
            return array('success' => false, 'error' => $e->getMessage());
        }
    }
    
    /**
     * Get CRA claims data for the approval table
     * @param int $craRequestId
     * @param string $accessToken
     * @return array Claims data with enriched information
     */
    public static function getCraClaimsData($craRequestId, $accessToken)
    {
        try {
            Yii::log("Getting CRA claims data for request ID: {$craRequestId}", CLogger::LEVEL_INFO, 'approval');

            // Get CRA request details first
            $craRequest = self::getCraRequest($craRequestId, $accessToken);
            if (!$craRequest) {
                Yii::log("Failed to retrieve CRA request for ID: {$craRequestId} - API may be unavailable or request not found", CLogger::LEVEL_ERROR, 'approval');
                return array();
            }

            // Get claims for this CRA request
            $claims = self::getCraClaims($craRequestId, $accessToken);
            if (empty($claims)) {
                Yii::log("No claims data retrieved for CRA request ID: {$craRequestId} - may indicate no claims exist or API connectivity issues", CLogger::LEVEL_WARNING, 'approval');
                return array();
            }

            // Enrich claims data with additional information
            $enrichedClaims = array();
            foreach ($claims as $claim) {
                $enrichedClaim = $claim;

                // Add brand and product information from request_ids
                if (isset($craRequest['requestIds']) && !empty($craRequest['requestIds'])) {
                    $brandProductInfo = self::getBrandProductInfo($craRequest['requestIds'], $accessToken);
                    // Join multiple brand names and product names with commas
                    $enrichedClaim['brandName'] = isset($brandProductInfo['brandNames']) && !empty($brandProductInfo['brandNames'])
                        ? implode(', ', $brandProductInfo['brandNames']) : '';
                    $enrichedClaim['productName'] = isset($brandProductInfo['productNames']) && !empty($brandProductInfo['productNames'])
                        ? implode(', ', $brandProductInfo['productNames']) : '';
                }

                // Add claim assessor information
                if (isset($craRequest['assigneeFullName']) && isset($craRequest['assigneeEmail'])) {
                    $enrichedClaim['claimAssessor'] = $craRequest['assigneeFullName'] . '(' . $craRequest['assigneeEmail'] . ')';
                }

                // Add exposition information
                if (isset($craRequest['expositionLevel']) && isset($craRequest['expositionDetail'])) {
                    $enrichedClaim['exposition'] = $craRequest['expositionLevel'] . ' - ' . $craRequest['expositionDetail'];
                }

                // Enrich robustness and fine/penalty information
                $enrichedClaim = self::enrichClaimDetails($enrichedClaim, $accessToken);

                $enrichedClaims[] = $enrichedClaim;
            }

            Yii::log("Successfully enriched " . count($enrichedClaims) . " claims", CLogger::LEVEL_INFO, 'approval');
            return $enrichedClaims;

        } catch (Exception $e) {
            Yii::log("Get claims data exception: " . $e->getMessage(), CLogger::LEVEL_ERROR, 'approval');
            return array();
        }
    }
    
    /**
     * Get CRA request details
     */
    private static function getCraRequest($craRequestId, $accessToken)
    {
        try {
            $url = self::$baseUrl . "/api/cra-requests/" . $craRequestId;

            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_TIMEOUT, 10);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Authorization: Bearer " . $accessToken
            ));

            $response = curl_exec($curl);
            $httpStatus = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);

            if ($curlError) {
                Yii::log("CURL error getting CRA request: " . $curlError, CLogger::LEVEL_ERROR, 'approval');
                return null;
            }

            if ($httpStatus == 200) {
                return json_decode($response, true);
            } else {
                Yii::log("HTTP error getting CRA request: Status {$httpStatus}, Response: {$response}", CLogger::LEVEL_ERROR, 'approval');
            }

            return null;
        } catch (Exception $e) {
            Yii::log("Exception getting CRA request: " . $e->getMessage(), CLogger::LEVEL_ERROR, 'approval');
            return null;
        }
    }
    
    /**
     * Get CRA claims for a specific request
     */
    private static function getCraClaims($craRequestId, $accessToken)
    {
        try {
            $url = self::$baseUrl . "/api/cra-claims?craId=" . $craRequestId;

            Yii::log("Getting CRA claims from URL: {$url}", CLogger::LEVEL_INFO, 'approval');

            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_TIMEOUT, 10);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Authorization: Bearer " . $accessToken
            ));

            $response = curl_exec($curl);
            $httpStatus = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);

            Yii::log("CRA claims API response - Status: {$httpStatus}, Response: " . substr($response, 0, 500), CLogger::LEVEL_INFO, 'approval');

            if ($curlError) {
                Yii::log("CURL error getting CRA claims: " . $curlError, CLogger::LEVEL_ERROR, 'approval');
                return array();
            }

            if ($httpStatus == 200) {
                $result = json_decode($response, true);

                // Handle both array response and object with data property
                if (is_array($result)) {
                    // Direct array response
                    $claims = $result;
                } else {
                    // Object response with data property
                    $claims = isset($result['data']) ? $result['data'] : array();
                }

                // Filter claims by craId to ensure we only get claims for this specific request
                $filteredClaims = array();
                foreach ($claims as $claim) {
                    if (isset($claim['craId']) && $claim['craId'] == $craRequestId) {
                        $filteredClaims[] = $claim;
                    }
                }

                Yii::log("Total claims from API: " . count($claims) . ", filtered for CRA ID {$craRequestId}: " . count($filteredClaims), CLogger::LEVEL_INFO, 'approval');
                return $filteredClaims;
            } else {
                Yii::log("HTTP error getting CRA claims: Status {$httpStatus}", CLogger::LEVEL_ERROR, 'approval');
            }

            return array();
        } catch (Exception $e) {
            Yii::log("Exception getting CRA claims: " . $e->getMessage(), CLogger::LEVEL_ERROR, 'approval');
            return array();
        }
    }
    
    /**
     * Get brand and product information from notification service
     * Fetches data from all notification IDs
     */
    private static function getBrandProductInfo($requestIds, $accessToken)
    {
        try {
            Yii::log("Getting brand/product info for requestIds: " . print_r($requestIds, true), CLogger::LEVEL_INFO, 'approval');

            // requestIds should be an array of notification IDs
            if (!is_array($requestIds) || empty($requestIds)) {
                Yii::log("Invalid requestIds format or empty array", CLogger::LEVEL_WARNING, 'approval');
                return array('brandNames' => array(), 'productNames' => array());
            }

            $brandNames = array();
            $productNames = array();

            // Iterate through all request IDs
            foreach ($requestIds as $requestId) {
                if (!empty($requestId)) {
                    $url = self::$baseUrl . "/api/notifications/" . $requestId;
                    Yii::log("Calling notifications API: " . $url, CLogger::LEVEL_INFO, 'approval');

                    $curl = curl_init($url);
                    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($curl, CURLOPT_TIMEOUT, 10);
                    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                        "Authorization: Bearer " . $accessToken
                    ));

                    $response = curl_exec($curl);
                    $httpStatus = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                    $curlError = curl_error($curl);
                    curl_close($curl);

                    Yii::log("Notifications API response for ID {$requestId} - Status: {$httpStatus}", CLogger::LEVEL_INFO, 'approval');

                    if (!$curlError && $httpStatus == 200) {
                        $notification = json_decode($response, true);
                        if ($notification) {
                            // Collect unique brand names
                            if (isset($notification['brandName']) && !empty($notification['brandName']) &&
                                !in_array($notification['brandName'], $brandNames)) {
                                $brandNames[] = $notification['brandName'];
                            }

                            // Collect unique product names
                            if (isset($notification['productName']) && !empty($notification['productName']) &&
                                !in_array($notification['productName'], $productNames)) {
                                $productNames[] = $notification['productName'];
                            }
                        }
                    } else {
                        Yii::log("Error fetching notification {$requestId}: Status {$httpStatus}, Error: {$curlError}", CLogger::LEVEL_ERROR, 'approval');
                    }
                }
            }

            return array(
                'brandNames' => $brandNames,
                'productNames' => $productNames
            );

            return array('brandName' => '', 'productName' => '');

        } catch (Exception $e) {
            Yii::log("Get brand/product info exception: " . $e->getMessage(), CLogger::LEVEL_ERROR, 'approval');
            return array('brandName' => '', 'productName' => '');
        }
    }
    
    /**
     * Enrich claim details with robustness and fine/penalty information
     */
    private static function enrichClaimDetails($claim, $accessToken)
    {
        // Get robustness information
        if (isset($claim['robustnessId']) && !empty($claim['robustnessId'])) {
            $robustness = self::getRobustnessInfo($claim['robustnessId'], $accessToken);
            if ($robustness) {
                $claim['robustnessName'] = $robustness['itemName'];
            }
        }
        
        // Get fine and penalty information
        if (isset($claim['fineAndPenaltyId']) && !empty($claim['fineAndPenaltyId'])) {
            $finePenalty = self::getFinePenaltyInfo($claim['fineAndPenaltyId'], $accessToken);
            if ($finePenalty) {
                $claim['fineAndPenaltyName'] = $finePenalty['itemName'];
            }
        }
        
        return $claim;
    }
    
    /**
     * Get robustness information
     */
    private static function getRobustnessInfo($robustnessId, $accessToken)
    {
        $url = self::$baseUrl . "/api/robustness/" . $robustnessId;
        
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "Authorization: Bearer " . $accessToken
        ));
        
        $response = curl_exec($curl);
        $httpStatus = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
        
        if ($httpStatus == 200) {
            return json_decode($response, true);
        }
        
        return null;
    }
    
    /**
     * Get fine and penalty information
     */
    private static function getFinePenaltyInfo($finePenaltyId, $accessToken)
    {
        $url = self::$baseUrl . "/api/fine-and-penalties/" . $finePenaltyId;
        
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "Authorization: Bearer " . $accessToken
        ));
        
        $response = curl_exec($curl);
        $httpStatus = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
        
        if ($httpStatus == 200) {
            return json_decode($response, true);
        }
        
        return null;
    }
    
    /**
     * Approve or reject a claim
     * @param int $claimId
     * @param string $action 'APPROVED' or 'NOT_APPROVED'
     * @param string $approverType Group name from authentication
     * @param string $accessToken
     * @return array Result of approval action
     */
    public static function approveClaim($claimId, $action, $approverType, $accessToken)
    {
        try {
            $url = self::$baseUrl . "/api/cra-claims/" . $claimId . "/approve";
            
            $data = array(
                'approvalStatus' => $action,
                'approverType' => $approverType
            );
            
            Yii::log("Approve claim - URL: {$url}", CLogger::LEVEL_INFO, 'approval');
            Yii::log("Approve claim - Data: " . print_r($data, true), CLogger::LEVEL_INFO, 'approval');
            
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/x-www-form-urlencoded",
                "Authorization: Bearer " . $accessToken
            ));
            
            $response = curl_exec($curl);
            $httpStatus = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);
            
            Yii::log("Approve claim response - Status: {$httpStatus}, Response: {$response}", CLogger::LEVEL_INFO, 'approval');
            
            if ($httpStatus == 200) {
                // API returned 200 but response might be empty
                if (empty($response)) {
                    Yii::log("API returned 200 but empty response - treating as success", CLogger::LEVEL_INFO, 'approval');
                }
                return array('success' => true);
            } else {
                $errorMessage = 'Approval action failed';

                // Provide specific error messages based on HTTP status
                if ($httpStatus == 401) {
                    $errorMessage = 'Authentication expired - please use a new link from your email';
                } elseif ($httpStatus == 403) {
                    $errorMessage = 'Access denied - insufficient permissions for this action';
                } elseif ($httpStatus == 404) {
                    $errorMessage = 'Claim not found or has been removed';
                } elseif ($httpStatus == 400) {
                    $errorMessage = 'Invalid approval request - claim may already be processed';
                } elseif ($httpStatus >= 500) {
                    $errorMessage = 'Approval service temporarily unavailable - please try again later';
                } else {
                    $errorData = json_decode($response, true);
                    if ($errorData && isset($errorData['message'])) {
                        $errorMessage = $errorData['message'];
                    } elseif (is_string($response) && !empty($response)) {
                        $errorMessage = $response;
                    }
                }

                return array('success' => false, 'error' => $errorMessage);
            }
            
        } catch (Exception $e) {
            Yii::log("Approve claim exception: " . $e->getMessage(), CLogger::LEVEL_ERROR, 'approval');
            return array('success' => false, 'error' => $e->getMessage());
        }
    }
}
